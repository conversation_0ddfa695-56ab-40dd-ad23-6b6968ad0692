# UBA Stacker Controller

This project is an ASP.NET Core web application designed to control and monitor one or more UBA bill stacker devices via a serial port connection. It provides a real-time interface using SignalR and a RESTful API for device management.

## Features

*   **Multi-Stacker Support**: Manage multiple stacker devices simultaneously, configured via `appsettings.json`.
*   **Real-Time Status Updates**: Uses SignalR to push real-time status updates from the stackers to connected clients.
*   **Configuration-Driven**: Easily switch between development (single stacker) and production (multiple stackers) environments using different `appsettings.json` files.
*   **Automatic Initialization**: Stackers are automatically initialized on application startup.
*   **REST API**: A simple REST API for interacting with the stackers (documented with Swagger).
*   **Object-Oriented Design**: A robust domain model (`Stacker` class) encapsulates device logic, making the system easy to maintain and extend.

## Getting Started

### Prerequisites

*   .NET 9 SDK (or the version specified in the `.csproj` file)
*   A compatible UBA bill stacker device connected via a serial port.
*   On Linux, your user must be part of the `dialout` group to access serial ports:
    ```bash
    sudo usermod -a -G dialout $USER
    ```
    (You will need to log out and log back in for this change to take effect).

### Configuration

The application uses `appsettings.json` for configuration. You can define the stacker devices in the `Stackers` section.

**Development (`appsettings.Development.json`):**

```json
{
  "Stackers": [
    {
      "Name": "DevStacker",
      "Port": "/dev/ttyS0", // Or COM3 on Windows
      "BaudRate": 9600
    }
  ]
}
```

**Production (`appsettings.json`):**

```json
{
  "Stackers": [
    {
      "Name": "Stacker1",
      "Port": "/dev/ttyS0",
      "BaudRate": 9600
    },
    {
      "Name": "Stacker2",
      "Port": "/dev/ttyS1",
      "BaudRate": 9600
    }
  ]
}
```

### Running the Application

1.  Clone the repository.
2.  Navigate to the project directory.
3.  Run the application using the desired launch profile (defined in `Properties/launchSettings.json`):

    ```bash
    # For both HTTP and HTTPS
    dotnet run --launch-profile https

    # For HTTP only
    dotnet run --launch-profile http
    ```

## API Usage

Once the application is running, you can access the Swagger UI at `/swagger` to view and interact with the API endpoints.

## SignalR Hub

The application uses a SignalR hub for real-time communication.

*   **Endpoint**: `/stackerHub`
*   **Client-side method to listen for status updates**: `ReceiveStackerStatus`
    *   **Payload**: `StackerState` object (`{ "name": "...", "status": "..." }`)
*   **Server-side method to send commands**: `SendCommand`
    *   **Arguments**: `stackerName` (string), `command` (string: "accept", "reject", "initialize")
