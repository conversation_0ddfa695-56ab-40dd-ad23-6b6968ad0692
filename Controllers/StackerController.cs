using Microsoft.AspNetCore.Mvc;
using UBAStackerController.Models;
using UBAStackerController.Services.Interfaces;

namespace UBAStackerController.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class StackerController : ControllerBase
    {
        private readonly IStackerService _stackerManager;

        public StackerController(IStackerService stackerManager)
        {
            _stackerManager = stackerManager;
        }

        [HttpGet]
        public ActionResult<IEnumerable<StackerState>> GetStackerStatus()
        {
            
            var StackerStates = _stackerManager.GetStackersState();

            return Ok(StackerStates);
        }
    }
}
