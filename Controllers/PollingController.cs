using Microsoft.AspNetCore.Mvc;
using UBAStackerController.Services.Interfaces;

namespace UBAStackerController.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class PollingController : ControllerBase
    {
        private readonly IPollingStateService _pollingStateService;

        public PollingController(IPollingStateService pollingStateService)
        {
            _pollingStateService = pollingStateService;
        }

        [HttpPost("pause")]
        public IActionResult PausePolling()
        {
            _pollingStateService.PausePolling();
            return Ok("Polling paused.");
        }

        [HttpPost("resume")]
        public IActionResult ResumePolling()
        {
            _pollingStateService.ResumePolling();
            return Ok("Polling resumed.");
        }
    }
}
