using System.Text.Json.Serialization;
using UBAStackerController.Hubs;
using ID003ProtocolManager;
using Microsoft.Extensions.Logging;
using UBAStackerController.Services.Interfaces;
using UBAStackerController.Services.Implementations;

var builder = WebApplication.CreateBuilder(args);

// Carga la configuración de "FanucRobot" desde appsettings.json
builder.Services.Configure<FanucRobotSettings>(
    builder.Configuration.GetSection("FanucRobot"));


// Add services to the container.
builder.Services.AddControllers().AddJsonOptions(options =>
{
    options.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter());
});
builder.Services.AddSignalR().AddJsonProtocol(options =>
{
    options.PayloadSerializerOptions.Converters.Add(new JsonStringEnumConverter());
});
builder.Services.AddLogging();

// Add CORS services
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowTunnels",
        builder =>
        {
            builder.WithOrigins("https://qsc5z4mj-7185.use2.devtunnels.ms", "http://localhost:5157", "https://localhost:7185")
                   .AllowAnyMethod()
                   .AllowAnyHeader()
                   .AllowCredentials();
        });
});

// Add Swagger services
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Register services for dependency injection.
builder.Services.AddSingleton<IStackerService, StackerService>();
builder.Services.AddSingleton<IFanucRobotService, FanucRobotService>();
builder.Services.AddSingleton<Sres.Net.EEIP.EEIPClient>();
builder.Services.AddSingleton<ID003CommandCreater>();
builder.Services.AddSingleton<IPollingStateService, PollingStateService>();

// Register the background service that polls the stacker.
builder.Services.AddHostedService<PollingBackgroundService>();

var app = builder.Build();

// Automatically initialize the stackers on startup
using (var scope = app.Services.CreateScope())
{
    var stackerManager = scope.ServiceProvider.GetRequiredService<IStackerService>();
    await stackerManager.InitializeStackers();
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
    app.UseSwagger();
    app.UseSwaggerUI();
}

// These are important for serving a potential frontend client app from wwwroot
app.UseDefaultFiles();
app.UseStaticFiles();

app.UseHttpsRedirection();

// Enable CORS
app.UseCors("AllowTunnels");

// Enable routing for SignalR and other endpoints.
app.UseRouting();

app.MapControllers();

// Map the SignalR hub to its endpoint.
// Clients will connect to ws://<your-server>/stackerHub
app.MapHub<StackerHub>("/stackerHub");

app.Run();