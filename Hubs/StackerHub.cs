using Microsoft.AspNetCore.SignalR;
using UBAStackerController.Services.Interfaces;

namespace UBAStackerController.Hubs
{
    public class StackerHub : Hub
    {
        private readonly ILogger<StackerHub> _logger;
        private readonly IStackerService _stackerManager;

        public StackerHub(ILogger<StackerHub> logger, IStackerService stackerManager)
        {
            _logger = logger;
            _stackerManager = stackerManager;
        }

        public async Task SendCommand(string stackerName, string command)
        {
            _logger.LogInformation($"Received command '{command}' for stacker '{stackerName}' from client.");

            var stacker = _stackerManager.GetStacker(stackerName);
            if (stacker == null)
            {
                await Clients.Caller.SendAsync("ReceiveError", $"Stacker '{stackerName}' not found.");
                return;
            }

            try
            {
                switch (command.ToLower())
                {
                    case "initialize":
                        await stacker.Initialize();
                        await Clients.Caller.SendAsync("CommandSent", $"Initialize command sent to {stackerName} successfully.");
                        break;
                    case "accept":
                        await stacker.Accept();
                        await Clients.Caller.SendAsync("CommandSent", $"Accept command sent to {stackerName} successfully.");
                        break;
                    default:
                        await Clients.Caller.SendAsync("ReceiveError", "Invalid command.");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error sending command to stacker {stackerName}.");
                await Clients.Caller.SendAsync("ReceiveError", $"Error sending command to stacker {stackerName}.");
            }
        }

        public override async Task OnConnectedAsync()
        {
            _logger.LogInformation($"Client connected: {Context.ConnectionId}");
            var stackerStates = _stackerManager.GetStackersState();
            await Clients.Caller.SendAsync("ReceiveInitialStackerStates", stackerStates);
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(System.Exception? exception)
        {
            _logger.LogInformation($"Client disconnected: {Context.ConnectionId}");
            if (exception != null)
            {
                _logger.LogError(exception, "Client disconnected with error.");
            }
            await base.OnDisconnectedAsync(exception);
        }
    }
}
