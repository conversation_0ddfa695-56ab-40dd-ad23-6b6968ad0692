using System.IO.Ports;

namespace UBAStackerController.Services.Implementations
{
    public class StackerConnectionService : IStackerConnectionService, IDisposable
    {
        private readonly ILogger<StackerConnectionService> _logger;
        public SerialPort Port { get; private set; }

        public StackerConnectionService(IConfiguration configuration, ILogger<StackerConnectionService> logger)
        {
            _logger = logger;
            var portName = configuration["SerialPort:Name"] ?? "COM3";
            var baudRate = int.Parse(configuration["SerialPort:BaudRate"] ?? "9600");

            Port = new SerialPort(portName, baudRate, Parity.Even, 8, StopBits.One);
            OpenPort();
        }

        public void OpenPort()
        {
            try
            {
                if (!Port.IsOpen)
                {
                    Port.Open();
                    _logger.LogInformation($"Serial port {Port.PortName} opened successfully.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Could not open serial port {Port.PortName}. Please check configuration and device connection.");
                // You might want to handle this more gracefully depending on your application's needs
                throw;
            }
        }

        public void ClosePort()
        {
            if (Port.IsOpen)
            {
                Port.Close();
                _logger.LogInformation($"Serial port {Port.PortName} closed.");
            }
        }

        public void Dispose()
        {
            ClosePort();
            Port.Dispose();
        }
    }
}
