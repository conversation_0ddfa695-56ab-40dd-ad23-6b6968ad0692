using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using UBAStackerController.Hubs;
using UBAStackerController.Services.Interfaces;
using Microsoft.Extensions.Options;

namespace UBAStackerController.Services.Implementations
{
    public class PollingBackgroundService : BackgroundService
    {
        private readonly ILogger<PollingBackgroundService> _logger;
        private readonly IHubContext<StackerHub> _hubContext;
        private readonly IStackerService _stackerManager;
        private readonly IPollingStateService _pollingStateService;
        private readonly IFanucRobotService _fanucRobotService;
        private readonly FanucRobotSettings _fanucRobotSettings;
        public PollingBackgroundService(
            IOptions<FanucRobotSettings> fanucRobotSettings,
            ILogger<PollingBackgroundService> logger,
            IHubContext<StackerHub> hubContext,
            IStackerService stackerManager,
            IPollingStateService pollingStateService,
            IFanucRobotService fanucRobotService)
        {   
            _fanucRobotSettings = fanucRobotSettings.Value;
            _logger = logger;
            _hubContext = hubContext;
            _stackerManager = stackerManager;
            _pollingStateService = pollingStateService;
            _fanucRobotService = fanucRobotService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("PollingBackgroundService is starting.");

            stoppingToken.Register(() =>
                _logger.LogInformation("PollingBackgroundService is stopping."));

            while (!stoppingToken.IsCancellationRequested)
            {
                if (!_pollingStateService.IsPollingPaused)
                {
                    foreach (var stacker in _stackerManager.Stackers.Values)
                    {
                        await stacker.UpdateStatus();
                        await _hubContext.Clients.All.SendAsync("ReceiveStackerStatus", stacker.CurrentState, stoppingToken);
                        await _fanucRobotService.WriteToRegisterAsync(_fanucRobotSettings.NumericRegister.Attribute, BitConverter.GetBytes((uint)stacker.CurrentState.Status));

                    }
                }

                await Task.Delay(50, stoppingToken);
            }

            _logger.LogInformation("PollingBackgroundService has stopped.");
        }
    }
}