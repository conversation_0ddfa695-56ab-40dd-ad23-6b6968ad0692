using System.Collections.Generic;
using System.IO.Ports;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using UBAStackerController.Models;
using ID003ProtocolManager;
using System;

using UBAStackerController.Services.Interfaces;

namespace UBAStackerController.Services.Implementations
{
   
    public class StackerService : IStackerService
    {
        private readonly ILogger<StackerService> _logger;
        private readonly ILoggerFactory _loggerFactory;
        private readonly IConfiguration _configuration;
        private readonly ID003CommandCreater _commandCreator;
        private readonly IPollingStateService _pollingStateService;
        public Dictionary<string, Stacker> Stackers { get; } = new Dictionary<string, Stacker>();

        public StackerService(IConfiguration configuration, ILogger<StackerService> logger, ILoggerFactory loggerFactory, ID003CommandCreater commandCreator, IPollingStateService pollingStateService)
        {
            _logger = logger;
            _loggerFactory = loggerFactory;
            _configuration = configuration;
            _commandCreator = commandCreator;
            _pollingStateService = pollingStateService;
        }

        public async Task InitializeStackers()
        {
            var stackerInfos = new List<StackerInfo>();
            _configuration.GetSection("Stackers").Bind(stackerInfos);

            foreach (var stackerInfo in stackerInfos)
            {
                try
                {
                    var port = new SerialPort(stackerInfo.Port, stackerInfo.BaudRate, Parity.Even, 8, StopBits.One);
                    port.ReadTimeout = 1000;
                    port.Open();

                    var stackerLogger = _loggerFactory.CreateLogger<Stacker>();
                    var stacker = new Stacker(stackerInfo.Name, port, _commandCreator, stackerLogger);

                    Stackers.Add(stackerInfo.Name, stacker);
                    await stacker.Initialize();
                    _logger.LogInformation($"Successfully initialized {stackerInfo.Name} on {stackerInfo.Port}.");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to initialize {stackerInfo.Name} on {stackerInfo.Port}.");
                }
            }
        }

        public Stacker? GetStacker(string name)
        {
            Stackers.TryGetValue(name, out var stacker);
            return stacker;
        }

        public List<StackerState> GetStackersState()
        {
            List<StackerState> stackersState = [];
            foreach (var stacker in Stackers)
            {
                stackersState.Add(stacker.Value.CurrentState);
            }

            return stackersState;
        }
    }
}
