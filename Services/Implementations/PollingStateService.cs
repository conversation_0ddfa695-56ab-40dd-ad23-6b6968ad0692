using UBAStackerController.Services.Interfaces;

namespace UBAStackerController.Services.Implementations
{
    public class PollingStateService : IPollingStateService
    {
        public bool IsPollingPaused { get; private set; }

        public void PausePolling()
        {
            IsPollingPaused = true;
        }

        public void ResumePolling()
        {
            IsPollingPaused = false;
        }
    }
}
