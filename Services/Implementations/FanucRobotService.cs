using Sres.Net.EEIP;
using Microsoft.Extensions.Options;
using UBAStackerController.Services.Interfaces;

namespace UBAStackerController.Services.Implementations
{
    public class FanucRobotService : IFanucRobotService
    {
        private readonly EEIPClient _eEIPClient;
        private readonly FanucRobotSettings _settings;
        private readonly ILogger<FanucRobotService> _logger;
        private readonly object _lock = new();
        private bool _robotIsConnected;
        private byte[]? _lastNumericWrittenValue;
        private byte[]? _lastStringWrittenValue;


        public FanucRobotService(EEIPClient eEIPClient, IOptions<FanucRobotSettings> settings, ILogger<FanucRobotService> logger)
        {
            _settings = settings.Value;
            _eEIPClient = eEIPClient;
            _logger = logger;
        }

        public async Task ReadRegisterAsync(int register)
        {
            if (!_settings.Enabled)
            {
                _logger.LogDebug("Fanuc integration disabled; ReadRegisterAsync skipped.");
                return;
            }

            await Task.Run(() =>
            {
                lock (_lock)
                {
                    try
                    {
                        EnsureConnected();
                        ushort classId = Convert.ToUInt16(_settings.NumericRegister.ClassId, 16);
                        var response = _eEIPClient.GetAttributeSingle(
                            classId,
                            _settings.NumericRegister.InstanceId,
                            _settings.NumericRegister.Attribute);
                        _logger.LogInformation("Read register {Attribute}: {Response}", _settings.NumericRegister.Attribute, BitConverter.ToString(response ?? Array.Empty<byte>()));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error reading Fanuc register");
                    }
                    finally
                    {
                        DisconnectIfConnected();
                    }
                }
            });
        }

        public async Task WriteToNumericRegisterAsync(int register, byte[] value)
        {
            if (!_settings.Enabled)
            {
                _logger.LogDebug("Fanuc integration disabled; WriteToRegisterAsync skipped.");
                return;
            }

            await Task.Run(() =>
            {
                lock (_lock)
                {
                    try
                    {
                        if (_lastNumericWrittenValue != null && value.SequenceEqual(_lastNumericWrittenValue))
                        {
                            _logger.LogTrace("Skipping write to Fanuc register because value has not changed.");
                            return;
                        }

                        EnsureConnected();
                        ushort classId = Convert.ToUInt16(_settings.NumericRegister.ClassId, 16);
                        _logger.LogDebug("Writing to register {Attribute} value {Value}", _settings.NumericRegister.Attribute, BitConverter.ToString(value));

                        var response = _eEIPClient.SetAttributeSingle(
                            classId,
                            _settings.NumericRegister.InstanceId,
                            register,
                            value);

                        _lastNumericWrittenValue = value.ToArray();
                        _logger.LogInformation("Write response: {Response}", BitConverter.ToString(response ?? Array.Empty<byte>()));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error writing Fanuc register");
                    }
                    finally
                    {
                        DisconnectIfConnected();
                    }
                }
            });
        }

        public async Task WriteToStringRegisterAsync(int register, string value)
        {
            if (!_settings.Enabled)
            {
                _logger.LogDebug("Fanuc integration disabled; WriteToRegisterAsync skipped.");
                return;
            }

            await Task.Run(() =>
            {
                lock (_lock)
                {
                    try
                    {
                        var valueBytes = System.Text.Encoding.UTF8.GetBytes(value);
                        if (_lastStringWrittenValue != null && valueBytes.SequenceEqual(_lastStringWrittenValue))
                        {
                            _logger.LogTrace("Skipping write to Fanuc register because value has not changed.");
                            return;
                        }

                        /*
                        String Format 
                        0–3 Byte       4-85 Bytes                  86-87 bytes (2 bytes)
                        String Length  String Register n (SR[n])    Padding
                        */

                        EnsureConnected();
                        ushort classId = Convert.ToUInt16(_settings.NumericRegister.ClassId, 16);
                        _logger.LogDebug("Writing to register {Attribute} value {Value}", _settings.NumericRegister.Attribute, BitConverter.ToString(valueBytes));
                        var string_len = valueBytes.Length;
                        var padding = (4 - (string_len % 4)) % 4;
                        var string_data = new byte[string_len + 1 + padding];
                        Array.Copy(valueBytes, string_data, string_len);
      
                        var response = _eEIPClient.SetAttributeSingle(
                            classId,
                            _settings.NumericRegister.InstanceId,
                            register,
                            valueBytes);

                        _lastNumericWrittenValue = valueBytes;
                        _logger.LogInformation("Write response: {Response}", BitConverter.ToString(response ?? Array.Empty<byte>()));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error writing Fanuc register");
                    }
                    finally
                    {
                        DisconnectIfConnected();
                    }
                }
            });
        }

        private void EnsureConnected()
        {
            if (!_robotIsConnected)
            {
                _eEIPClient.RegisterSession(_settings.IpAddress);
                _robotIsConnected = true;
                _logger.LogInformation("Registered Fanuc EEIP session to {Ip}", _settings.IpAddress);
            }
        }

        private void DisconnectIfConnected()
        {
            if (_robotIsConnected)
            {
                _eEIPClient.UnRegisterSession();
                _robotIsConnected = false;
                _logger.LogInformation("Unregistered Fanuc EEIP session.");
            }
        }
    }
}