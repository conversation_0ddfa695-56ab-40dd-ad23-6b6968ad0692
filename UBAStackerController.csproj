<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.4" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    <PackageReference Include="System.IO.Ports" Version="9.0.8" />
  </ItemGroup>

  <!--
  #####################################################################
  ### REFERENCIAS A LAS LIBRERÍAS MANUALES (DLLS) EN LA CARPETA 'lib' ###
  #####################################################################
  -->
  <ItemGroup>
    <Reference Include="EEIP">
      <HintPath>lib/lib/EEIP.dll</HintPath>
    </Reference>

    <Reference Include="ID003ProtocolManager">
      <HintPath>lib/ID003ProtocolManager.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
