using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using UBAStackerController.Models;

namespace UBAStackerController.Utils
{
    public static class ResponseParser
    {
        public static StackerStatus ParseStatus(byte[] status, ILogger logger)
        {
            if (status == null || status.Length < 3)
            {
                logger.LogError("Invalid status response");
                return StackerStatus.Unknown;
            }

            switch (status[2])
            {
                case 0x11: return StackerStatus.Idling;
                case 0x13: return StackerStatus.BillInEscrow;
                case 0x17: return StackerStatus.Rejected;
                case 0x18: return StackerStatus.Returning;
                case 0x19: return StackerStatus.Holding;
                case 0x1A: return StackerStatus.Inhibited;
                case 0x1B: return StackerStatus.Initialize;
                case 0x40: return StackerStatus.PowerUp;
                case 0x43: return StackerStatus.StackerFull;
                case 0x44: return StackerStatus.StackerOpen;
                case 0x45: return StackerStatus.JamInAcceptor;
                case 0x46: return StackerStatus.JamInStacker;
                case 0x47: return StackerStatus.Paused;
                case 0x48: return StackerStatus.Cheated;
                case 0x49: return StackerStatus.MajorFailure;
                case 0x4A: return StackerStatus.CommunicationError;
                default: 
                    logger.LogWarning($"Unknown Status: {status[2]:X2}");
                    return StackerStatus.Unknown;
            }
        }

        public static string ParseEscrowStatus(byte escrowData, ILogger logger)
        {
            switch (escrowData)
            {
                case 0x64: return "$1000 In Escrow";
                case 0x65: return "$2000 In Escrow";
                case 0x66: return "$5000 In Escrow";
                case 0x67: return "$10000 In Escrow";
                case 0x68: return "$20000 In Escrow";
                default: return "Bill in Escrow";
            }
        }

        public static List<int> ParseRecyclerTotalCountResponse(byte[] response, int length, ILogger logger)
        {
            // TODO: Load state from previously suspended application
            throw new NotImplementedException();
        }
    }
}
