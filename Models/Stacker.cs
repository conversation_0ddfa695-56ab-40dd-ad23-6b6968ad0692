using ID003ProtocolManager;
using System.IO.Ports;
using UBAStackerController.Models;
using UBAStackerController.Utils;

namespace UBAStackerController.Models
{
    public class Stacker
    {
        private readonly ILogger<Stacker> _logger;
        private readonly ID003CommandCreater _commandCreator;
        private readonly SerialPort _port;
        private readonly byte[] _buffer = new byte[255];
        private readonly byte[] _status = new byte[255];
        public string Name { get; }
        public StackerState CurrentState { get; private set; }

        public Stacker(string name, SerialPort port, ID003CommandCreater commandCreator, ILogger<Stacker> logger)
        {
            Name = name;
            _port = port;
            _commandCreator = commandCreator;
            _logger = logger;
            CurrentState = new StackerState
            {
                Name = name,
                Status = StackerStatus.PowerUp,
                Port = _port.PortName,
                IsActive = true,
                LastUpdated = DateTime.Now.ToShortTimeString()
            };
        }

        public async Task Initialize()
        {
            _logger.LogInformation($"Initializing stacker {Name}...");

            byte enable1 = 0;
            byte enable2 = 0;

            //Sending the reset command
            _commandCreator.Reset(_buffer);
            int length = _buffer[1];
            _port.Write(_buffer, 0, length);
            await Task.Delay(100);
            int bytesRead = _port.Read(_status, 0, _status.Length);

            // Enabling denominations
            _commandCreator.SetEnableDeno(_buffer, enable1, enable2);
            length = _buffer[1];
            _port.Write(_buffer, 0, length);
            await Task.Delay(100);

            //Setting standard security enable1= 0x00, enable2= 0x00
            _commandCreator.SetSecurity(_buffer, enable1, enable2);
            length = _buffer[1];
            _buffer[5] = 0x01;
            await Task.Delay(10);
            _port.Write(_buffer, 0, length);
            await Task.Delay(100);

            //No optional functions enable1= 0x00, enable2= 0x00
            _commandCreator.SetOpFunction(_buffer, enable1, enable2);
            length = _buffer[1];
            _port.Write(_buffer, 0, length);
            await Task.Delay(100);

            //Enabling bill aceptor enable1= 0x00, enable2= 0x00
            _commandCreator.SetInhibit(_buffer, enable1);
            length = _buffer[1];
            _port.Write(_buffer, 0, length);
            await Task.Delay(100);

            await GetCurrentCount();
        }
        public async Task UpdateStatus()
        {
            if (!_port.IsOpen)
            {
                CurrentState.Status = StackerStatus.CommunicationError;
                CurrentState.IsActive = false;
                return;
            }

            try
            {
                _commandCreator.StatusRequest(_buffer);
                int length = _buffer[1];
                _port.Write(_buffer, 0, length);

                await Task.Delay(100);

                int bytesRead = _port.Read(_status, 0, _status.Length);

                if (bytesRead > 0)
                {
                    CurrentState.Status = ResponseParser.ParseStatus(_status, _logger);
                    _logger.LogInformation(CurrentState.Status.ToString());
                    var dateNow = DateTime.Now;
                    CurrentState.LastUpdated = $"{dateNow:dd/MM/yy H:mm}";

                    if (CurrentState.Status == StackerStatus.BillInEscrow)
                    {
                        await Accept(); // Automatically accept the bill in escrow
                    }

                    // await GetCurrentCount();

                }
                else
                {
                    _logger.LogError("No se recibieron bytes");
                }
            }
            catch (TimeoutException ex)
            {
                _logger.LogError(ex, $"Error during read response for stacker {Name}. Timeout");
                CurrentState.Status = StackerStatus.CommunicationError;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating status for stacker {Name}.");
                CurrentState.Status = StackerStatus.CommunicationError;
            }
        }

        public async Task Accept()
        {
            _logger.LogInformation($"Sending ACCEPT command to {Name}.");
            bool vend = false; //value to check if vend valid has been sent
            _commandCreator.Stack1(_buffer); //we have detected escrow and now sending the stack command.
            int length = _buffer[1];
            _port.Write(_buffer, 0, length);
            await Task.Delay(100);

            while (!vend) //if no vend valid, keep checking status
            { 
                _commandCreator.StatusRequest(_buffer); //checking status
                length = _buffer[1];
                _port.Write(_buffer, 0, length);
                _port.Read(_status, 0, 255); //capturing status from the com port
                await Task.Delay(100);

                if (_status[2] == 0x15) //we have received vend valid response
                {
                    _commandCreator.Ack(_buffer);  //Sending an ACK
                    length = _buffer[1];
                    _port.Write(_buffer, 0, length);
                    vend = true;
                }
            }
        }

        public async Task GetCurrentCount()
        {
            _logger.LogInformation($"Get current count from {Name}.");
            _commandCreator.RecyclerTotalCountRequest(_buffer);
            int length = _buffer[1];
            _port.Write(_buffer, 0, length);
            await Task.Delay(100);
            int bytesRead = _port.Read(_status, 0, 255);

            if (bytesRead > 0)
            {
                var counts = ResponseParser.ParseRecyclerTotalCountResponse(_status, bytesRead, _logger);
                for (int i = 0; i < counts.Count; i++)
                {
                    _logger.LogInformation($"Recycler Box #{i + 1}: {counts[i]} bills");
                    CurrentState.BillInEscrow = counts[i];
                }
            }
        }
    }
}