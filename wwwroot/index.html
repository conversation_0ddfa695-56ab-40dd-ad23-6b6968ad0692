<!DOCTYPE html>
<html>
<head>
    <title>UBA Stacker Controller</title>
    <style>
        body { font-family: sans-serif; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #dddddd; text-align: left; padding: 8px; }
        th { background-color: #f2f2f2; }
        .controls { margin-bottom: 10px; }
    </style>
</head>
<body>
    <h1>UBA Stacker Controller</h1>

    <div class="controls">
        <button id="pause-polling">Pause Polling</button>
        <button id="resume-polling">Resume Polling</button>
    </div>

    <div class="controls">
        <select id="stacker-select"></select>
        <button id="send-initialize">Initialize</button>
        <button id="send-accept">Accept</button>
    </div>

    <table id="stackers-table">
        <thead>
            <tr>
                <th>Name</th>
                <th>Status</th>
                <th>Bill In Escrow</th>
                <th>Is Active</th>
                <th>Last Updated</th>
                <th>Port</th>
            </tr>
        </thead>
        <tbody>
        </tbody>
    </table>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    <script>
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/stackerHub")
            .build();

        function updateTable(stacker) {
            const tableBody = document.querySelector('#stackers-table tbody');
            let row = tableBody.querySelector(`#stacker-${stacker.name}`);
            if (!row) {
                row = document.createElement('tr');
                row.id = `stacker-${stacker.name}`;
                tableBody.appendChild(row);

                const stackerSelect = document.getElementById('stacker-select');
                const option = document.createElement('option');
                option.value = stacker.name;
                option.text = stacker.name;
                stackerSelect.appendChild(option);
            }

            row.innerHTML = `
                <td>${stacker.name}</td>
                <td>${stacker.status}</td>
                <td>${stacker.billInEscrow}</td>
                <td>${stacker.isActive}</td>
                <td>${stacker.lastUpdated}</td>
                <td>${stacker.port}</td>
            `;
        }

        connection.on("ReceiveInitialStackerStates", (stackers) => {
            const tableBody = document.querySelector('#stackers-table tbody');
            tableBody.innerHTML = '';
            const stackerSelect = document.getElementById('stacker-select');
            stackerSelect.innerHTML = '';
            stackers.forEach(stacker => {
                updateTable(stacker);
            });
        });

        connection.on("ReceiveStackerStatus", (stacker) => {
            updateTable(stacker);
        });

        document.getElementById('pause-polling').addEventListener('click', async () => {
            await fetch('/api/polling/pause', { method: 'POST' });
        });

        document.getElementById('resume-polling').addEventListener('click', async () => {
            await fetch('/api/polling/resume', { method: 'POST' });
        });

        document.getElementById('send-initialize').addEventListener('click', async () => {
            const stackerName = document.getElementById('stacker-select').value;
            await connection.invoke("SendCommand", stackerName, "initialize");
        });

        document.getElementById('send-accept').addEventListener('click', async () => {
            const stackerName = document.getElementById('stacker-select').value;
            await connection.invoke("SendCommand", stackerName, "accept");
        });

        async function start() {
            try {
                await connection.start();
                console.log("SignalR Connected.");
            } catch (err) {
                console.log(err);
                setTimeout(start, 5000);
            }
        };

        connection.onclose(async () => {
            await start();
        });

        start();
    </script>
</body>
</html>